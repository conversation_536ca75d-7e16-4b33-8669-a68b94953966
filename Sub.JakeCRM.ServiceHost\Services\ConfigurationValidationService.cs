using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Sub.JakeCRM.Application.Services;

namespace Sub.JakeCRM.ServiceHost.Services;

/// <summary>
/// Service that validates configuration on application startup
/// </summary>
public class ConfigurationValidationService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ConfigurationValidationService> _logger;

    public ConfigurationValidationService(
        IServiceProvider serviceProvider,
        ILogger<ConfigurationValidationService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting configuration validation...");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var configurationService = scope.ServiceProvider.GetRequiredService<IConfigurationService>();

            // Validate configuration
            var validationResult = configurationService.ValidateConfiguration();

            if (!validationResult.IsValid)
            {
                _logger.LogCritical("Configuration validation failed. Application cannot start.");
                
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogError("Configuration Error: {Error}", error);
                }

                // Throw exception to prevent application startup
                throw new InvalidOperationException($"Configuration validation failed with {validationResult.Errors.Count} errors. See logs for details.");
            }

            // Log warnings if any
            foreach (var warning in validationResult.Warnings)
            {
                _logger.LogWarning("Configuration Warning: {Warning}", warning);
            }

            _logger.LogInformation("Configuration validation completed successfully");

            // Log configuration summary
            LogConfigurationSummary(configurationService);
        }
        catch (Exception ex)
        {
            _logger.LogCritical(ex, "Critical error during configuration validation");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        // No cleanup needed for configuration validation
        return Task.CompletedTask;
    }

    private void LogConfigurationSummary(IConfigurationService configurationService)
    {
        try
        {
            _logger.LogInformation("=== Configuration Summary ===");

            // Log key configuration values (masked for security)
            var settings = configurationService.GetAllSettings();
            var importantSettings = new[]
            {
                "Application:Name",
                "Application:Version",
                "Application:Environment",
                "ServiceBus:QueueName",
                "ServiceBus:TopicName",
                "ServiceBus:MaxConcurrentCalls",
                "ApiClient:BaseUrl",
                "ApiClient:Timeout",
                "ApiClient:RetryCount",
                "Processing:EnableBatching",
                "Processing:EnableParallelProcessing",
                "Monitoring:EnableCustomMetrics",
                "Monitoring:HealthChecks:Enabled",
                "Security:EnableEncryption",
                "Security:EnableAuditLogging"
            };

            foreach (var setting in importantSettings)
            {
                if (settings.TryGetValue(setting, out var value))
                {
                    _logger.LogInformation("{Setting}: {Value}", setting, value);
                }
            }

            _logger.LogInformation("=== End Configuration Summary ===");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log configuration summary");
        }
    }
}
